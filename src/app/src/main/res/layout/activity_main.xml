<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context="com.bodymount.app.MainActivity">

    <LinearLayout
        android:id="@+id/fullLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="0dp"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <LinearLayout
            android:id="@+id/pauseAlarmView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="7"
            android:background="@color/red"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="7"
                android:paddingTop="3dp"
                android:paddingBottom="3dp"
                android:src="@drawable/pause"
                app:tint="@color/white"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/pauseAlarmText"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <Button
                android:id="@+id/cancelPause"
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="7"
                android:background="@color/ap_transparent"
                android:gravity="end"
                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                android:theme="@style/fontAwesomeText" />

        </LinearLayout>

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/appToolbar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="-3dp"
            android:layout_marginEnd="-3dp"
            android:layout_weight="7"
            app:titleTextAppearance="@style/Toolbar.TitleText"
            app:titleTextColor="@color/white">

            <LinearLayout
                android:id="@+id/sensorInfo"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="end"
                android:layout_marginEnd="80dp">

                <LinearLayout
                    android:id="@+id/chestSensInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:padding="3dp"
                    android:visibility="gone">
                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_sensor_chest" />

                    <TextView
                        android:id="@+id/chestSensName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="3dp"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/white" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/limbSensInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:padding="3dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_sensor_limb" />

                    <TextView
                        android:id="@+id/limbSensName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="3dp"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/white" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/bpSensInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:padding="3dp"
                    android:visibility="visible">
                    <ImageView
                        android:id="@+id/sensorBatteryIndicatorIv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_battery_full_solid" />
                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_sensor_bp" />

                    <TextView
                        android:id="@+id/bpSensName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="3dp"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/white" />
                </LinearLayout>

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="90"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="83"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <LinearLayout
                    android:id="@+id/outerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:baselineAligned="false"
                    android:orientation="horizontal">

                    <ScrollView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="9"
                        android:fillViewport="true">

                        <LinearLayout
                            android:id="@+id/chartViewLayout"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            tools:ignore="RtlSymmetry">

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/ecgChartOuter"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="5dp"
                                android:layout_weight="1"
                                app:cardBackgroundColor="@color/gray"
                                app:cardCornerRadius="4dp"
                                app:strokeColor="@color/gray"
                                app:strokeWidth="2dp"
                                tools:ignore="NestedWeights">

                                <com.github.mikephil.charting.charts.LineChart
                                    android:id="@+id/ecgChart"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/gray"
                                    android:visibility="visible" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="start|top"
                                    android:autoSizeTextType="uniform"
                                    android:background="@drawable/rounded_edit_text"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:paddingBottom="5dp"
                                    android:text="@string/lead1"
                                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                    android:textColor="@color/white"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/ecgScale"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="start|bottom"
                                    android:autoSizeTextType="uniform"
                                    android:background="@drawable/rounded_edit_text"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:paddingBottom="5dp"
                                    android:text="@string/ecgZoom"
                                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                    android:textColor="@color/white"
                                    android:textStyle="bold"
                                    android:visibility="invisible" />

                                <TextView
                                    android:id="@+id/ecgConnectionAlerts"
                                    style="@style/noConnectionText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/no_chest_sensor_connected"
                                    android:visibility="visible" />
                                <!--                           style="@style/noConnectionText"
                                                            android:text="@string/no_chest_sensor_connected"-->

                                <LinearLayout
                                    android:id="@+id/hrAlarmBox"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|top"
                                    android:background="@drawable/rounded_alarm_view"
                                    android:orientation="horizontal"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/ecgAlarmSilenceText"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="15dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="5dp"
                                        android:paddingBottom="3dp" />

                                    <TextView
                                        android:id="@+id/ecgAlarmPrefix"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="5dp"
                                        android:paddingBottom="3dp" />

                                    <TextView
                                        android:id="@+id/ecgAlarmText"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="15dp"
                                        android:paddingBottom="3dp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:orientation="horizontal">

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/respChartOuter"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="5dp"
                                    android:layout_weight="1"
                                    android:padding="5dp"
                                    app:cardBackgroundColor="@color/gray"
                                    app:cardCornerRadius="4dp"
                                    app:strokeColor="@color/gray"
                                    app:strokeWidth="2dp">


                                    <com.github.mikephil.charting.charts.LineChart
                                        android:id="@+id/respChart"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="@color/gray"
                                        android:visibility="visible" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="start|top"
                                        android:autoSizeTextType="uniform"
                                        android:background="@drawable/rounded_edit_text"
                                        android:paddingStart="10dp"
                                        android:paddingEnd="10dp"
                                        android:paddingBottom="5dp"
                                        android:text="@string/lead2"
                                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                        android:textColor="@color/white"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/respConnectionAlerts"
                                        style="@style/noConnectionText"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/no_chest_sensor_connected"
                                        android:visibility="visible" />
                                    <!--style="@style/noConnectionText"
                                    android:text="@string/no_chest_sensor_connected"-->
                                    <LinearLayout
                                        android:id="@+id/rrAlarmBox"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="end|top"
                                        android:background="@drawable/rounded_alarm_view"
                                        android:orientation="horizontal"
                                        android:visibility="gone">

                                        <TextView
                                            android:id="@+id/respAlarmSilenceText"
                                            style="@style/TextAppearance.AppCompat.Small"
                                            android:layout_width="wrap_content"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:paddingStart="15dp"
                                            android:paddingTop="3dp"
                                            android:paddingEnd="5dp"
                                            android:paddingBottom="3dp" />

                                        <TextView
                                            android:id="@+id/respAlarmPrefix"
                                            style="@style/TextAppearance.AppCompat.Small"
                                            android:layout_width="wrap_content"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:paddingStart="5dp"
                                            android:paddingTop="3dp"
                                            android:paddingEnd="5dp"
                                            android:paddingBottom="3dp" />

                                        <TextView
                                            android:id="@+id/respAlarmText"
                                            style="@style/TextAppearance.AppCompat.Small"
                                            android:layout_width="wrap_content"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:paddingStart="5dp"
                                            android:paddingTop="3dp"
                                            android:paddingEnd="15dp"
                                            android:paddingBottom="3dp" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>
                            </LinearLayout>

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/respChartOuter3"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="5dp"
                                android:layout_weight="1"
                                android:padding="5dp"
                                app:cardBackgroundColor="@color/gray"
                                app:cardCornerRadius="4dp"
                                app:strokeColor="@color/gray"
                                app:strokeWidth="2dp">

                                <com.github.mikephil.charting.charts.LineChart
                                    android:id="@+id/ecgChart3"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/gray"
                                    android:visibility="visible" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="start|top"
                                    android:autoSizeTextType="uniform"
                                    android:background="@drawable/rounded_edit_text"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:paddingBottom="5dp"
                                    android:text="@string/leadv"
                                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                    android:textColor="@color/white"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/respConnectionAlerts3"
                                    style="@style/noConnectionText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/no_chest_sensor_connected"
                                    android:visibility="visible" />
                                <!-- style="@style/noConnectionText"
                                 android:text="@string/no_chest_sensor_connected"-->
                                <LinearLayout
                                    android:id="@+id/rrAlarmBox3"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|top"
                                    android:background="@drawable/rounded_alarm_view"
                                    android:orientation="horizontal"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/respAlarmSilenceText3"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="15dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="5dp"
                                        android:paddingBottom="3dp" />

                                    <TextView
                                        android:id="@+id/respAlarmPrefix3"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="5dp"
                                        android:paddingBottom="3dp" />

                                    <TextView
                                        android:id="@+id/respAlarmText3"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="15dp"
                                        android:paddingBottom="3dp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/spo2ChartOuter"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="5dp"
                                android:layout_weight="1"
                                android:padding="5dp"
                                android:visibility="gone"
                                app:cardBackgroundColor="@color/gray"
                                app:cardCornerRadius="4dp"
                                app:strokeColor="@color/gray"
                                app:strokeWidth="2dp">

                                <com.github.mikephil.charting.charts.LineChart
                                    android:id="@+id/spo2Chrt"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@color/gray"
                                    android:visibility="visible" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="start|top"
                                    android:autoSizeTextType="uniform"
                                    android:background="@drawable/rounded_edit_text"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:paddingBottom="5dp"
                                    android:text="@string/PLETH"
                                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                    android:textColor="@color/white"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/spo2ConnectionAlerts"
                                    style="@style/noConnectionText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/no_chest_sensor_connected"
                                    android:visibility="visible" />
                                <!--<TextView
                                    android:id="@+id/spo2ConnectionAlerts"
                                    style="@style/noConnectionText"
                                    android:text="@string/no_limb_sensor_connected" />-->

                                <LinearLayout
                                    android:id="@+id/spo2AlarmBox"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|top"
                                    android:background="@drawable/rounded_alarm_view"
                                    android:orientation="horizontal"
                                    android:visibility="gone">

                                    <TextView
                                        android:id="@+id/spo2AlarmSilenceText"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="15dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="5dp"
                                        android:paddingBottom="3dp" />

                                    <TextView
                                        android:id="@+id/spo2AlarmPrefix"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="5dp"
                                        android:paddingBottom="3dp" />

                                    <TextView
                                        android:id="@+id/spo2AlarmText"
                                        style="@style/TextAppearance.AppCompat.Small"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:paddingStart="5dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="15dp"
                                        android:paddingBottom="3dp" />
                                </LinearLayout>

                            </com.google.android.material.card.MaterialCardView>

                        </LinearLayout>
                    </ScrollView>


                    <LinearLayout
                        android:id="@+id/rightPanel"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="38"
                        android:orientation="vertical"
                        tools:ignore="RtlSymmetry">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/hrCard"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp"
                            tools:ignore="NestedWeights">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:padding="10dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="2.8"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView12"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:text="@string/hr"
                                        android:gravity="center|left"
                                        tools:ignore="NestedWeights" />

                                    <TextView
                                        android:id="@+id/hrHighVal"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:gravity="end"
                                        android:textColor="@color/lightGreen"
                                        android:visibility="visible" />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/hrLowVal"
                                    style="@style/footerTopLabels"
                                    android:layout_marginTop="-5dp"
                                    android:layout_weight="2.95"
                                    android:gravity="end"
                                    android:textColor="@color/lightGreen"
                                    android:visibility="visible" />

                                <TextView
                                    android:id="@+id/hrValue"
                                    style="@style/sideBarVitals"
                                    android:layout_weight="1.0"
                                    android:textColor="@color/lightGreen"
                                    android:visibility="invisible" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/tempCard"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:paddingStart="10dp"
                                android:paddingTop="5dp"
                                android:paddingEnd="10dp"
                                android:paddingBottom="5dp">

<!--                                <TextView-->
<!--                                    style="@style/footerTopLabels"-->
<!--                                    android:layout_marginBottom="-2dp"-->
<!--                                    android:layout_weight="2"-->
<!--                                    android:text="@string/temp"-->
<!--                                    android:gravity="center|left"-->
<!--                                    tools:ignore="NestedWeights" />-->

                                <TextView
                                    android:id="@+id/textViewTemp"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="2"
                                    android:layout_marginBottom="-2dp"
                                    android:gravity="center|left"
                                    android:text="@string/temp"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    android:id="@+id/temp"
                                    style="@style/footerVitals"
                                    android:layout_marginTop="20dp"
                                    android:layout_marginBottom="-7dp"
                                    android:layout_weight="1.3"
                                    android:gravity="center|center_vertical"
                                    android:visibility="invisible"
                                    android:orientation="horizontal"
                                    android:textColor="@color/spo2Blue"
                                    tools:ignore="TooManyViews" />

                                <TextView
                                    android:id="@+id/tempSensor"
                                    style="@style/footerTopLabels"
                                    android:layout_marginTop="-2dp"
                                    android:layout_weight="1.5"
                                    android:visibility="invisible"
                                    android:gravity="top|center"
                                    tools:ignore="NestedWeights" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/bpCard"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:visibility="visible"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:paddingStart="10dp"
                                android:paddingTop="5dp"
                                android:paddingEnd="10dp"
                                android:paddingBottom="5dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginBottom="-2dp"
                                    android:layout_weight="5"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView3"
                                        style="@style/footerTopLabels"
                                        android:textStyle="normal"
                                        android:layout_weight="2"
                                        android:gravity="center|left"
                                        android:text="@string/NIBP"
                                        tools:ignore="NestedWeights" />

                                    <TextView
                                        android:id="@+id/auto_timer"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="2"
                                        android:gravity="center|end"
                                        android:visibility="gone"
                                        tools:ignore="SmallSp" />

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="end|center"
                                        android:layout_weight="2"
                                        android:paddingStart="5dp"
                                        android:paddingEnd="1dp"
                                        android:visibility="gone"
                                        android:src="@drawable/timer" />

                                </LinearLayout>


                                <LinearLayout
                                    android:id="@+id/bpVitalLayer"
                                    style="@style/footerMiddleLayer"
                                    android:orientation="vertical"
                                    android:visibility="visible"
                                    android:paddingBottom="5dp"
                                    tools:ignore="TooManyViews">

                                    <!-- Main BP values row -->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="50dp"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/bp_measuring_indicator"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:text="Measuring"
                                            android:textColor="@color/white"
                                            android:textSize="18sp"
                                            android:textStyle="bold"
                                            android:visibility="gone" />

                                        <TextView
                                            android:id="@+id/bpSys"
                                            style="@style/footerVitals"
                                            android:layout_weight="5"
                                            android:gravity="end|center_vertical"
                                            android:textColor="@color/pink"
                                            android:visibility="visible"
                                            tools:ignore="NestedWeights" />

                                        <TextView
                                            android:id="@+id/line"
                                            style="@style/footerVitals"
                                            android:layout_weight="6"
                                            android:gravity="center"
                                            android:text="@string/foreSlash"
                                            android:textColor="@color/pink"
                                            android:visibility="visible"
                                            tools:ignore="NestedWeights" />

                                        <TextView
                                            android:id="@+id/bpDia"
                                            style="@style/footerVitals"
                                            android:layout_weight="5"
                                            android:gravity="start|center_vertical"
                                            android:textColor="@color/pink"
                                            android:visibility="visible"
                                            tools:ignore="NestedWeights" />

                                    </LinearLayout>

                                    <!-- Measuring indicator below BP values -->
                                    <TextView
                                        android:id="@+id/bp_measuring_text"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="Measuring..."
                                        android:textColor="@color/white"
                                        android:textSize="13sp"
                                        android:textStyle="italic"
                                        android:visibility="gone"
                                        android:layout_marginTop="2dp" />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/bpTime"
                                    style="@style/footerTopLabels"
                                    android:layout_marginTop="-2dp"
                                    android:layout_weight="1.5"
                                    android:gravity="top|center"
                                    android:visibility="gone"
                                    android:text="@string/noValue"
                                    tools:ignore="SmallSp,TooManyViews" />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/bpError"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/red"
                                android:gravity="center"
                                android:textColor="@color/white"
                                android:visibility="gone" />

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/rrCard"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:padding="5dp"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:padding="10dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="3"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView16"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:text="PR"
                                        tools:ignore="TooDeepLayout" />

                                    <TextView
                                        android:id="@+id/prHighVal"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:gravity="end"
                                        android:textColor="@color/white"
                                        android:visibility="visible" />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/prLowVal"
                                    style="@style/footerTopLabels"
                                    android:layout_marginTop="-5dp"
                                    android:layout_weight="3.1"
                                    android:gravity="end"
                                    android:textColor="@color/white"
                                    android:visibility="visible" />

                                <TextView
                                    android:id="@+id/respValue"
                                    style="@style/sideBarVitals"
                                    android:visibility="invisible"
                                    android:layout_weight="1.2"
                                    android:textColor="@color/white" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/spo2Card"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:padding="5dp"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:padding="10dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="3"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView20"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:text="@string/spo2"
                                        tools:ignore="TooDeepLayout" />

                                    <TextView
                                        android:id="@+id/spo2HighVal"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:gravity="end"
                                        android:textColor="@color/spo2Blue"
                                        android:visibility="visible" />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/spo2LowVal"
                                    style="@style/footerTopLabels"
                                    android:layout_marginTop="-5dp"
                                    android:layout_weight="3.1"
                                    android:gravity="end"
                                    android:textColor="@color/spo2Blue"
                                    android:visibility="visible" />

                                <TextView
                                    android:id="@+id/spo2value"
                                    style="@style/sideBarVitals"
                                    android:layout_marginStart="-5dp"
                                    android:layout_marginEnd="-5dp"
                                    android:layout_weight="1.2"
                                    android:visibility="invisible"
                                    android:textColor="@color/spo2Blue" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="16"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/footer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="5dp"
                    android:background="@color/background"
                    android:baselineAligned="false"
                    android:orientation="horizontal">


                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/deviceInfoCv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="0.9"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <!-- Top Center Title -->
                            <TextView
                                style="@style/footerTopLabels"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_marginTop="8dp"
                                android:text="Device ID"
                                android:textStyle="bold"
                                android:textSize="16sp"/>

                            <!-- Device Number -->
                            <TextView
                                android:id="@+id/deviceModelNumber"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_marginTop="-5dp"
                                android:text="999"
                                android:textSize="38sp"
                                android:textColor="@color/lightGreen"
                                android:fontFamily="@font/open_sans_bold_font"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/positionCard"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight=".75"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:paddingStart="10dp"
                            android:paddingTop="5dp"
                            android:paddingEnd="10dp"
                            android:paddingBottom="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginBottom="-2dp"
                                android:layout_weight="1.5"
                                android:orientation="horizontal"
                                tools:ignore="TooManyViews">

                                <TextView
                                    android:id="@+id/textView4"
                                    style="@style/footerTopLabels"
                                    android:layout_weight=".63"
                                    android:gravity="center|start"
                                    android:text="@string/rollPosition"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    android:id="@+id/turnTimeSetting"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:gravity="center|end"
                                    tools:ignore="SmallSp" />

                                <ImageView
                                    android:id="@+id/turnTimerIcon"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="end|center"
                                    android:layout_weight="1.375"
                                    android:paddingStart="5dp"
                                    android:paddingEnd="1dp"
                                    android:src="@drawable/timer" />

                            </LinearLayout>


                            <LinearLayout
                                android:id="@+id/positionVitalLayer"
                                style="@style/footerMiddleLayer"
                                android:orientation="horizontal"
                                android:visibility="invisible"
                                tools:ignore="TooManyViews">

                                <TextView
                                    android:id="@+id/bodyAngle"
                                    style="@style/footerVitals"
                                    android:layout_weight="1.1"
                                    android:gravity="center|center_vertical"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    android:id="@+id/bodyPosition"
                                    style="@style/footerVitals"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:text="@string/noValue"
                                    android:textAllCaps="false"
                                    android:textColor="@color/white"
                                    android:visibility="gone" />

                                <TextView
                                    android:id="@+id/left"
                                    style="@style/footerVitals"
                                    android:layout_weight="1.375"
                                    android:gravity="center"
                                    android:text="@string/L"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights,TooManyViews" />

                                <TextView
                                    android:id="@+id/back"
                                    style="@style/footerVitals"
                                    android:layout_weight="1.375"
                                    android:gravity="center"
                                    android:text="@string/B"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    android:id="@+id/right"
                                    style="@style/footerVitals"
                                    android:layout_weight="1.375"
                                    android:gravity="center"
                                    android:text="@string/R"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    android:id="@+id/turnTime"
                                    style="@style/footerVitals"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:paddingStart="5dp"
                                    android:paddingEnd="2dp"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights,VisualLintBounds" />

                            </LinearLayout>


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginTop="-2dp"
                                android:layout_weight="1.5"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/text_second_bottom"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1.1"
                                    android:gravity="top|center"
                                    android:text="@string/rollAngle"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:gravity="top|center"
                                    android:text="@string/position" />

                                <TextView
                                    android:id="@+id/labelTurnTime"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:gravity="top|center"
                                    android:text="@string/turnTime" />

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/activityCard"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight=".88"
                        android:visibility="gone"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:paddingStart="10dp"
                            android:paddingTop="5dp"
                            android:paddingEnd="10dp"
                            android:paddingBottom="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginBottom="-2dp"
                                android:layout_weight="1.5"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/textView5"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:gravity="center|start"
                                    android:text="@string/activity"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_width="79dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center|end"
                                    android:visibility="invisible"
                                    tools:ignore="SmallSp,VisualLintBounds" />

                                <Chronometer
                                    android:id="@+id/positionTime"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="5"
                                    android:gravity="center"
                                    android:text="@string/activity"
                                    tools:ignore="NestedWeights,VisualLintBounds" />

                            </LinearLayout>


                            <LinearLayout
                                android:id="@+id/activityVitalLayer"
                                style="@style/footerMiddleLayer"
                                android:orientation="horizontal"
                                android:visibility="invisible"
                                tools:ignore="TooManyViews">

                                <TextView
                                    android:id="@+id/stepCount"
                                    style="@style/footerVitals"
                                    android:layout_weight="1"
                                    android:gravity="center|center_vertical"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    android:id="@+id/fall_alert"
                                    style="@style/footerVitals"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:paddingStart="5dp"
                                    android:paddingEnd="2dp"
                                    android:textColor="@color/white"
                                    tools:ignore="NestedWeights" />

                            </LinearLayout>


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginTop="-2dp"
                                android:layout_weight="1.5"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/stepCountLabel"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:gravity="top|center"
                                    android:text="@string/step_count"
                                    tools:ignore="NestedWeights" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:gravity="top|center"
                                    android:text="@string/fallAlert"
                                    tools:ignore="SmallSp" />

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>


                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/pauseAlarm"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:paddingStart="5dp"
                            android:paddingTop="5dp"
                            android:paddingEnd="5dp"
                            android:paddingBottom="5dp">

                            <androidx.legacy.widget.Space
                                style="@style/footerTopLabels"
                                android:layout_marginBottom="-2dp"
                                android:layout_weight="1.5"
                                tools:ignore="NestedWeights,SmallSp" />

                            <ImageView
                                android:id="@+id/img_alarm"
                                style="@style/footerTopLabels"
                                android:layout_gravity="center"
                                android:layout_marginTop="-20dp"
                                android:layout_weight="1.7"
                                android:padding="5dp"
                                android:src="@drawable/pause"
                                app:tint="@color/white" />

                            <TextView
                                style="@style/footerTopLabels"
                                android:layout_marginTop="-2dp"
                                android:layout_weight="1.5"
                                android:gravity="top|center"
                                android:text="@string/pauseAlarms" />

                        </LinearLayout>

                        <!--<LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:paddingStart="10dp"
                            android:paddingTop="5dp"
                            android:paddingEnd="10dp"
                            android:paddingBottom="5dp">

                            <ImageView
                                android:id="@+id/img_alarm"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:layout_weight="2.5"
                                android:padding="5dp"
                                android:src="@drawable/pause"
                                app:tint="@color/white" />

                            <TextView
                                style="@style/footerTopLabels"
                                android:layout_weight="4"
                                android:gravity="bottom|center"
                                android:text="@string/pauseAlarms" />

                        </LinearLayout>-->
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/scanCard"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="0.9"
                        android:backgroundTint="@color/gray"
                        android:padding="5dp"
                        app:cardCornerRadius="4dp"
                        tools:ignore="NestedWeights">

                        <TextView
                            android:id="@+id/textConnect"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:autoSizeTextType="uniform"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/gray"
                            android:gravity="center"
                            android:padding="5dp"
                            android:text="@string/tapToConnect"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/appLogo"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1.033"
                        android:backgroundTint="@color/background"
                        android:padding="5dp"
                        app:cardCornerRadius="4dp"
                        tools:ignore="NestedWeights">

                        <ImageView
                            android:id="@+id/applogo"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/rounded_edit_text"
                            android:contentDescription="@string/app_name"
                            android:gravity="center"
                            android:src="@drawable/iorbit_logo_no_text" />
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>